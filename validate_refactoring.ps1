Write-Host "=== Validation de la refonte de configuration ===" -ForegroundColor Green

Write-Host "Verification des fichiers crees..." -ForegroundColor Yellow

$files = @(
    "tests/sample_configs/unified.config",
    "include/unified_config_file.h", 
    "src/unified_config_file.c"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "OK: $file existe" -ForegroundColor Green
    } else {
        Write-Host "ERREUR: $file manquant" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Resume des changements ===" -ForegroundColor Cyan
Write-Host "1. Fichier unifie cree: tests/sample_configs/unified.config"
Write-Host "2. Nouveau parser: src/unified_config_file.c"
Write-Host "3. CLI modifie: -c au lieu de -g -t -a -s"
Write-Host "4. main.c adapte"
Write-Host "5. Documentation mise a jour"
Write-Host ""
Write-Host "Nouvelle syntaxe:" -ForegroundColor Yellow
Write-Host "  Ancien: ./f200 -g global.config -t tcp.config -a hl7.config -s storing.config"
Write-Host "  Nouveau: ./f200 -c unified.config"
Write-Host ""
Write-Host "Refonte terminee avec succes!" -ForegroundColor Green
