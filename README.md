# Synlab automatons drivers

## Building

### Prerequisites

Make sure these tools are available on your system :

* [libconfuse](https://github.com/libconfuse/libconfuse)
* [cmake](https://cmake.org/)
* [libxml2](https://gitlab.gnome.org/GNOME/libxml2)
* [libserialport](https://sigrok.org/wiki/Libserialport)

### Compile instructions

Build it as a cmake project :

```bash
mkdir build && cd build
cmake ..
make
```

The compiled binaries will then be available under the `build/bin` directory.

## ***TLDR***

```bash
./<mydriver> -g <global_config_file> -t <transport_config_file> -a <application_config_file> -s <storing_config_file>
```

e.g. F200 TCP/IP HL7 being in the `build` directory:

```bash
./bin/f200 \
    -g ./../tests/sample_configs/global.config \
    -t ./../tests/sample_configs/tcp.config \
    -a ./../tests/sample_configs/hl7.config \
    -s ./../tests/sample_configs/storing.config
```

**WARNING**  This won't just work out of the box, the parameters should be adapted to the tesing environnement (e.g. bind-ip for TCP/IP using F200 over TCP).

## Testing

The script `tests/tester.py` allows you to select and launch testing scenarios. It outputs the commands it runs, allowing you to try and test for yourself.

```bash
cd testing
./tester.py
```

***Note*** : *you need [python3](https://www.python.org/downloads/) installed on your system to run this script.*

## External librairies

* [argtable3](https://github.com/argtable/argtable3) : CLI parser
* [libconfuse](https://github.com/libconfuse/libconfuse) : configuration file reader
* [log.c](https://github.com/rxi/log.c) : logger
* [libxml2](https://gitlab.gnome.org/GNOME/libxml2) : xml parser
* [libserialport](https://sigrok.org/wiki/Libserialport) : RS232 protocol

## External tools used

* [clang-format](https://clang.llvm.org/docs/ClangFormat.html) : formatter
* [Doxygen](https://www.doxygen.nl/index.html) : documentation generator
* [include-what-you-use](https://github.com/include-what-you-use/include-what-you-use) : `#include` directives cleaner
* [cppcheck](https://github.com/danmar/cppcheck) : linter

## Project structure

```bash
.
├── external    # External libraries
│   ├── <lib1>
│   ├── <lib2>
│   └── ...
├── include     # Headers
├── lib         # Internal (i.e. self-written) libraries 
│   ├── <lib1>
│   ├── <lib2>
│   └── ...
├── src         # Source files
│   ├── application_layer   # Implementations of application layer protocols
│   ├── storing_layer       # Implementations of storing layer functions
│   ├── transport_layer     # Implementations of transport layer protocols
│   └── ...
├── tests       # Testing material
│    ├── simulators  # Automatons simulation tools
│    └── specs   # Messages specifications extracted from documentations
└── tools       # Auxiliary tools, to maintain the codebase
```

## Global architecture

The drivers are split in 3 layers using a **publish/subscribe** pattern. The layers are **tansport** (TCP/IP, RS232, ...), **application** (HL7, POCT, ...) and **storing** (how to store messages in the system). The publish/subscribe pattern allows layers to wait for and publish messages, allowing them to communicate with each other. **Each layer is agnostic of the other layers** : they communicate using interfaces ; any combination of protocols should work.

For now, these are the implemented protocols :

* tansport : TCP/IP, RS232
* application : HL7 2.4, 2.5 and 2.6, POCT1
* storing : a simple storing system using grouping folders

More protocols will be added in the future.

## Drivers CLI

Drivers CLI take 4 fours arguments, corresponding to distinct configuration files :

* global configuration : logs output folder, daemon mode, ...
* application level configuration : HL7, POCT, ... specific settings
* transport level configuration : TCP/IP, RS232, ... specific settings
* storing level configuration : storing system specific settings

To specify the settings you want, look under `testing/sample_configs/` : these are example settings, that you can copy and modify at will.
  
## C Guidelines used throughout this project

These guidelines can be either considered **good practice**s, or be **arbitrary** choices. The former case must be justified from a programmatic point of view, while the second is just a choice, followed throughout the project for style coherence.

### Check for pointer nullity as booleans (arbitrary)

```c
if (!ptr) {
    ...
}
```

It it [guaranted by the standard](https://stackoverflow.com/questions/1284050/c-comparison-to-null) to be safe.

### Don't emulate methods in structs (good practice)

Instead of this :

```c
typedef struct {
    ...
    void (*bark)();  
} Dog;
...
static void dog_bark(Dog* dog);

void init_struct(Dog* dog) {
    dog->bark = dog_bark;
}
```

Just prefix all the functions regarding the name of the struct with the struct name :

```c
typedef struct {
    ...
} Dog;
...
void dog_bark(Dog* dog);

```

This :

* doesn't try to use C an object-oriented language
* avoids the init function
* prevents syntax like `dog->bark(dog)` but instead `dog_bark(dog)`, which is considered clearer
* allows to you to directly got to the function definition/declaration while implementing/debugging

Note that the method pattern should be used when a struct actually needs to store a non compiled-time fixed function pointer.

See [this thread](https://stackoverflow.com/questions/17052443/c-function-inside-struct) for more furnished discussion.

### Use goto for memory freeing, and ONLY for memory freeing (good practice/arbitrary)

Gotos are a good way to handle errors in functions where memory freeing is necessary, and multiple error paths.

See this example without gotos :

```c
int dog_bark(Dog * dog) {
    void * barking_generator = barking_generator_create();
    if (!barking_generator) {
        return 1;
    }

    char* barking_message = barking_generator_generate();
    if (!barking_message) {
        barking_generator_destruct();
        return 1;
    } 

    void * barking_displayer = barking_displayer_create();
    if (!barking_displayer) {
        free(barking_message);
        barking_generator_destruct();
        return 1;
    }

    barking_displayer(barking_message);
    free(barking_message);
    barking_generator_destruct();
    barking_displayer_destruct();

    return 0;
}
```

Can be rewritten like so :

```c
#define REQUIRE_CONDITION(condition, goto_flag) \
    if (!(condition)) {                         \
        status = 1;                             \
        goto goto_flag;                         \
    }

int dog_bark(Dog * dog) {
    void * barking_generator;
    char* barking_message;
    void * barking_displayer ;
    int status;

    barking_generator = barking_generator_create();
    REQUIRE_CONDITION(barking_generator, dog_bark_clean);

    barking_message = barking_generator_generate();
    REQUIRE_CONDITION(barking_message, dog_bark_clean);
    
    barking_displayer = barking_displayer_create();
    REQUIRE_CONDITION(barking_displayer, dog_bark_clean);

    barking_displayer(barking_message);

dog_bark_clean:
    if (barking_generator) 
        barking_generator_destruct(barking_generator);
    if (barking_message) 
        free(barking_message);
    if (barking_displayer) 
        barking_displayer_destruct(barking_displayer);

    return status;
}
```

While the code is not necessarily shorter (but in practice, it is), this allows for easier memory handling for error paths. The code structure is clearer as well : memory allocation happens in one place, memory freeing happens in one place.

Note that goto usage outside of this case is prohibited, as this instruction can cause unexpected behaviour.
