#include "hl7_config_file.h"

#include "log.h"
#include <confuse.h>
#include <locale.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define VERIFY_KEY(condition, name)                                            \
  if (!(condition)) {                                                          \
    log_error("Missing key : %s", name);                                       \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

#define CHECK_CFG_ERROR(condition, ...)                                        \
  if (!(condition)) {                                                          \
    log_error(__VA_ARGS__);                                                    \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

int application_config_file_parse(ApplicationConfiguration *config,
                                  const char *path) {
  int status = 0;

  // specification
  cfg_opt_t opts[] = {
      CFG_SIMPLE_INT(CONFIG_DELAY_BEFORE_ACK, &config->delay_before_ack),
      CFG_SIMPLE_STR(CONFIG_SENDING_APPLICATION, &config->sending_application),
      CFG_SIMPLE_STR(CONFIG_SENDING_FACILITY, &config->sending_facility),
      CFG_END()};

  // loading
  cfg_t *cfg = cfg_init(opts, 0);
  int return_code = cfg_parse(cfg, path);

  CHECK_CFG_ERROR(
      return_code != CFG_FILE_ERROR,
      "can't load configuration file %s : confuse can't access the file", path);

  CHECK_CFG_ERROR(
      return_code != CFG_PARSE_ERROR,
      "can't load configuration file %s : confuse reported a parse error",
      path);

  // verification
  VERIFY_KEY(config->sending_application != NULL, CONFIG_SENDING_APPLICATION);
  VERIFY_KEY(config->sending_facility != NULL, CONFIG_SENDING_FACILITY);

clean_exit:
  cfg_free(cfg);

  return status;
}
