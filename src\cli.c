#include "cli.h"

#include "argtable3.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

static void print_help(const char *in_program, void *argtable[]) {
  log_info("Usage: %s", in_program);
  arg_print_syntax(stdout, argtable, "\n");
  arg_print_glossary(stdout, argtable, "  %-43s %s\n");
  log_space(LOG_INFO);
}

int cli_parse(CLIArguments *cli_arguments, const int argc, const char **argv,
              const char *program) {
  struct arg_lit *cli_help;
  struct arg_str *cli_global_config;
  struct arg_str *cli_transport_config;
  struct arg_str *cli_application_config;
  struct arg_str *cli_storing_config;
  struct arg_end *cli_end;

  void *argtable[] = {
      cli_help = arg_litn(CLI_HELP_SHORT, CLI_HELP, 0, 1, "print this help"),
      cli_global_config =
          arg_strn(CLI_GLOBAL_CONFIG_SHORT, CLI_GLOBAL_CONFIG, "<path>", 1, 1,
                   "global configuration file path"),
      cli_transport_config =
          arg_strn(CLI_TRANSPORT_CONFIG_SHORT, CLI_TRANSPORT_CONFIG, "<path>",
                   1, 1, "transport configuration file path"),
      cli_application_config =
          arg_strn(CLI_APPLICATION_CONFIG_SHORT, CLI_APPLICATION_CONFIG,
                   "<path>", 1, 1, "application configuration file path"),
      cli_storing_config =
          arg_strn(CLI_STORING_CONFIG_SHORT, CLI_STORING_CONFIG, "<path>", 1, 1,
                   "storing configuration file path"),
      cli_end = arg_end(20)};

  if (arg_nullcheck(argtable)) {
    log_error("cli contains NULL values");
    return 1;
  }

  if (arg_parse(argc, (char **)argv, argtable) > 0) {
    arg_print_errors(stdout, cli_end, program);
    print_help(program, argtable);
    return 1;
  }

  if (cli_help->count > 0) {
    print_help(program, argtable);
    return 1;
  }

  cli_arguments->global_config = strdup(cli_global_config->sval[0]);
  cli_arguments->transport_config = strdup(cli_transport_config->sval[0]);
  cli_arguments->application_config = strdup(cli_application_config->sval[0]);
  cli_arguments->storing_config = strdup(cli_storing_config->sval[0]);

  arg_freetable(argtable, sizeof(argtable) / sizeof(argtable[0]));

  return 0;
}

void cli_destruct(CLIArguments *cli_arguments) {
  if (cli_arguments->application_config)
    free(cli_arguments->application_config);
  if (cli_arguments->global_config)
    free(cli_arguments->global_config);
  if (cli_arguments->storing_config)
    free(cli_arguments->storing_config);
  if (cli_arguments->transport_config)
    free(cli_arguments->transport_config);
}
