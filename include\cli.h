#pragma once

#include "global_config.h"
#include <stdlib.h>

#define CLI_HELP "help"
#define CLI_HELP_SHORT "h"
#define CLI_GLOBAL_CONFIG "global-config"
#define CLI_GLOBAL_CONFIG_SHORT "g"
#define CLI_TRANSPORT_CONFIG "transport-config"
#define CLI_TRANSPORT_CONFIG_SHORT "t"
#define CLI_APPLICATION_CONFIG "application-config"
#define CLI_APPLICATION_CONFIG_SHORT "a"
#define CLI_STORING_CONFIG "storing-config"
#define CLI_STORING_CONFIG_SHORT "s"

typedef struct {
  /**
   * @brief Global configuration filepath.
   */
  char *global_config;
  /**
   * @brief Transport layer configuration filepath.
   */
  char *transport_config;
  /**
   * @brief Application layer configuration filepath.
   */
  char *application_config;
  /**
   * @brief Storing layer configuration filepath.
   */
  char *storing_config;
} CLIArguments;

int cli_parse(CLIArguments *cli_arguments, const int argc, const char **argv,
              const char *program);

void cli_destruct(CLIArguments *cli_arguments);