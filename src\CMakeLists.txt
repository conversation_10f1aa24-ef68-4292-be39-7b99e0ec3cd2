file(GLOB_RECURSE TCP_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/transport_layer/tcp/*.c")
file(GLOB_RECURSE RS232_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/transport_layer/rs232/*.c")
file(GLOB_RECURSE HL7_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/application_layer/hl7/*.c")
file(GLOB_RECURSE POCT_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/application_layer/poct/*.c")
file(GLOB_RECURSE FILE_STORING_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/storing_layer/file_storing/*.c")

file(GLOB_RECURSE INTERFACES_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/interfaces/*.c")
set(MAIN_SOURCES main.c cli.c global_config.c unified_config_file.c
                 ${INTERFACES_SOURCES})
set(MAIN_LIBRARIES
    log
    daemon
    pubsub
    argtable3
    confuse
    binary_buffer
    directory)

# ##############################################################################
# TESTS
# ##############################################################################

include(CTest)

# ##############################################################################
# f200
# ##############################################################################

# tcp

add_executable(f200 ${MAIN_SOURCES} ${TCP_LAYER_SOURCES} ${HL7_LAYER_SOURCES}
                    ${FILE_STORING_LAYER_SOURCES})

target_link_libraries(f200 ${MAIN_LIBRARIES} tcp_server hl7_260)

target_compile_definitions(
  f200
  PRIVATE HL7
  PRIVATE HL7_VERSION=260
  PRIVATE TCP
  PRIVATE FILE_STORING)

# rs232

find_library(SERIALPORT_LIBRARY NAMES serialport)
if(SERIALPORT_LIBRARY)
  add_executable(f200_rs232 ${MAIN_SOURCES} ${RS232_LAYER_SOURCES}
                            ${HL7_LAYER_SOURCES} ${FILE_STORING_LAYER_SOURCES})

  target_link_libraries(f200_rs232 ${MAIN_LIBRARIES} tcp_server hl7_260
                        ${SERIALPORT_LIBRARY})

  target_compile_definitions(
    f200_rs232
    PRIVATE HL7
    PRIVATE HL7_VERSION=260
    PRIVATE RS232
    PRIVATE FILE_STORING)
else()
  message(WARNING "libserialport not found")
endif()

# ##############################################################################
# gem5000
# ##############################################################################

add_executable(gem5000 ${MAIN_SOURCES} ${TCP_LAYER_SOURCES}
                       ${HL7_LAYER_SOURCES} ${FILE_STORING_LAYER_SOURCES})

target_link_libraries(gem5000 ${MAIN_LIBRARIES} tcp_server hl7_240)

target_compile_definitions(
  gem5000
  PRIVATE HL7
  PRIVATE HL7_VERSION=240
  PRIVATE TCP
  PRIVATE FILE_STORING)

# ##############################################################################
# hemocue Hb201DM
# ##############################################################################

find_package(LibXml2)

if(LibXml2_FOUND)
  add_executable(hb201dm ${MAIN_SOURCES} ${TCP_LAYER_SOURCES}
                         ${POCT_LAYER_SOURCES} ${FILE_STORING_LAYER_SOURCES})

  target_include_directories(hb201dm PRIVATE ${LIBXML2_INCLUDE_DIRS})

  target_link_libraries(hb201dm ${MAIN_LIBRARIES} tcp_server poct
                        ${LIBXML2_LIBRARIES})

  target_compile_definitions(
    hb201dm
    PRIVATE HB201DM
    PRIVATE POCT
    PRIVATE POCT_VERSION=1A2
    PRIVATE TCP
    PRIVATE FILE_STORING)
else()
  message(WARNING "libxml2 not found, Hemocue Hb201DM not built")
endif()

# ##############################################################################
# aqt
# ##############################################################################

add_executable(aqt ${MAIN_SOURCES} ${TCP_LAYER_SOURCES} ${HL7_LAYER_SOURCES}
                   ${FILE_STORING_LAYER_SOURCES})

target_link_libraries(aqt ${MAIN_LIBRARIES} tcp_server hl7_250)

target_compile_definitions(
  aqt
  PRIVATE HL7
  PRIVATE HL7_VERSION=250
  PRIVATE TCP
  PRIVATE FILE_STORING)
