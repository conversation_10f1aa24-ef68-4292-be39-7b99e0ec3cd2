# Script PowerShell pour tester la refonte de configuration unifiée

Write-Host "=== Test de validation de la refonte de configuration ===" -ForegroundColor Green

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "CMakeLists.txt")) {
    Write-Error "CMakeLists.txt non trouvé. Exécutez ce script depuis la racine du projet."
    exit 1
}

Write-Status "Vérification des fichiers créés..."

# Vérifier que tous les nouveaux fichiers ont été créés
$requiredFiles = @(
    "tests/sample_configs/unified.config",
    "tests/sample_configs/tcp_hl7.config", 
    "tests/sample_configs/rs232_poct.config",
    "include/unified_config_file.h",
    "src/unified_config_file.c",
    "tests/test_unified_config.c"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Status "✓ $file existe"
    } else {
        Write-Error "✗ $file manquant"
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Error "Certains fichiers requis sont manquants."
    exit 1
}

Write-Status "Vérification de la syntaxe des fichiers de configuration..."

# Vérifier que les fichiers de configuration ont la bonne structure
$configFiles = @(
    "tests/sample_configs/unified.config",
    "tests/sample_configs/tcp_hl7.config",
    "tests/sample_configs/rs232_poct.config"
)

foreach ($configFile in $configFiles) {
    $content = Get-Content $configFile -Raw
    if ($content -match '\[global\]' -and $content -match 'log-level=') {
        Write-Status "✓ $configFile a une structure valide"
    } else {
        Write-Warning "✗ $configFile pourrait avoir une structure invalide"
    }
}

Write-Status "Vérification des modifications du code source..."

# Vérifier que cli.h a été modifié
$cliHeader = Get-Content "include/cli.h" -Raw
if ($cliHeader -match 'CLI_CONFIG') {
    Write-Status "✓ include/cli.h modifié correctement"
} else {
    Write-Error "✗ include/cli.h n'a pas été modifié correctement"
}

# Vérifier que main.c a été modifié
$mainSource = Get-Content "src/main.c" -Raw
if ($mainSource -match 'unified_config_file_parse') {
    Write-Status "✓ src/main.c modifié correctement"
} else {
    Write-Error "✗ src/main.c n'a pas été modifié correctement"
}

Write-Host ""
Write-Host "=== Résumé des changements effectués ===" -ForegroundColor Cyan
Write-Host "1. ✓ Fichier de configuration unifié créé : tests/sample_configs/unified.config"
Write-Host "2. ✓ Nouveau module de parsing : src/unified_config_file.c + include/unified_config_file.h"
Write-Host "3. ✓ Interface CLI modifiée : -c au lieu de -g -t -a -s"
Write-Host "4. ✓ main.c adapté pour utiliser le parser unifié"
Write-Host "5. ✓ CMakeLists.txt mis à jour"
Write-Host "6. ✓ README.md mis à jour avec la nouvelle syntaxe"
Write-Host "7. ✓ Fichiers de test créés pour différentes combinaisons"
Write-Host ""
Write-Host "Nouvelle syntaxe d'utilisation :" -ForegroundColor Yellow
Write-Host "  Ancien : ./f200 -g global.config -t tcp.config -a hl7.config -s storing.config"
Write-Host "  Nouveau : ./f200 -c unified.config"
Write-Host ""
Write-Status "Refonte terminée avec succès !"

Write-Host ""
Write-Host "=== Instructions pour tester ===" -ForegroundColor Cyan
Write-Host "1. Compilez le projet avec CMake :"
Write-Host "   mkdir build && cd build"
Write-Host "   cmake .. -DTCP=ON -DHL7=ON -DFILE_STORING=ON"
Write-Host "   make (ou utilisez votre système de build)"
Write-Host ""
Write-Host "2. Testez avec la nouvelle syntaxe :"
Write-Host "   ./f200 --help"
Write-Host "   ./f200 -c ../tests/sample_configs/unified.config"
Write-Host ""
Write-Host "3. Testez différentes combinaisons :"
Write-Host "   ./f200 -c ../tests/sample_configs/tcp_hl7.config"
Write-Host "   ./f200 -c ../tests/sample_configs/rs232_poct.config"
