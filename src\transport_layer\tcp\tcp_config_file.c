#include "tcp_config_file.h"

#include "log.h"
#include <confuse.h>
#include <locale.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define VERIFY_KEY(condition, name)                                            \
  if (!(condition)) {                                                          \
    log_error("Missing key : %s", name);                                       \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

#define CHECK_CFG_ERROR(condition, ...)                                        \
  if (!(condition)) {                                                          \
    log_error(__VA_ARGS__);                                                    \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

int transport_config_file_parse(TransportConfiguration *config,
                                const char *path) {
  int status = 0;

  // specification
  cfg_opt_t opts[] = {
      CFG_SIMPLE_STR(CONFIG_BIND_IP, &config->bind_ip),
      CFG_SIMPLE_INT(CONFIG_BIND_PORT, &config->bind_port),
      CFG_SIMPLE_INT(CONFIG_RECV_TIMEOUT, &config->recv_timeout), CFG_END()};

  // loading
  cfg_t *cfg = cfg_init(opts, 0);
  int return_code = cfg_parse(cfg, path);

  CHECK_CFG_ERROR(
      return_code != CFG_FILE_ERROR,
      "can't load configuration file %s : confuse can't access the file", path);

  CHECK_CFG_ERROR(
      return_code != CFG_PARSE_ERROR,
      "can't load configuration file %s : confuse reported a parse error",
      path);

  // verification
  VERIFY_KEY(config->bind_ip != NULL, CONFIG_BIND_IP);

clean_exit:
  cfg_free(cfg);

  return status;
}
