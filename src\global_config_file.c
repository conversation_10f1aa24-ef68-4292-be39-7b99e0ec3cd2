#include "global_config_file.h"

#include "log.h"
#include <confuse.h>
#include <locale.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define VERIFY_KEY(condition, name)                                            \
  if (!(condition)) {                                                          \
    log_error("Missing key : %s", name);                                       \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

#define CHECK_CFG_ERROR(condition, ...)                                        \
  if (!(condition)) {                                                          \
    log_error(__VA_ARGS__);                                                    \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

int global_config_file_parse(GlobalConfiguration *config, const char *path) {
  int status = 0;

  cfg_bool_t daemonize;

  // specification
  cfg_opt_t opts[] = {CFG_SIMPLE_STR(CONFIG_LOG_LEVEL, &config->log_level),
                      CFG_SIMPLE_BOOL(CONFIG_DAEMONIZE, &daemonize),
                      CFG_SIMPLE_INT(CONFIG_STOP_AFTER, &config->stop_after),
                      CFG_SIMPLE_STR(CONFIG_LOG_FILE, &config->log_file),
                      CFG_END()};

  // loading
  cfg_t *cfg = cfg_init(opts, 0);
  int return_code = cfg_parse(cfg, path);

  CHECK_CFG_ERROR(
      return_code != CFG_FILE_ERROR,
      "can't load configuration file %s : confuse can't access the file", path);

  CHECK_CFG_ERROR(
      return_code != CFG_PARSE_ERROR,
      "can't load configuration file %s : confuse reported a parse error",
      path);

  // verification
  VERIFY_KEY(config->log_level != NULL, CONFIG_LOG_LEVEL);
  VERIFY_KEY(config->log_file != NULL, CONFIG_LOG_FILE);

  // transforming type
  config->daemonize = (long int)daemonize;

clean_exit:
  cfg_free(cfg);

  return status;
}
