/**
 * @file unified_config_file.c
 * @brief Implementation of unified configuration file parser.
 */

#include "unified_config_file.h"
#include "global_config_file.h"
#include "log.h"
#include <confuse.h>
#include <stdlib.h>
#include <string.h>

// Macro for error checking (reused from existing config files)
#define CHECK_CFG_ERROR(condition, message, ...)                              \
  if (!(condition)) {                                                          \
    log_error(message, ##__VA_ARGS__);                                         \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

#define VERIFY_KEY(condition, key)                                             \
  CHECK_CFG_ERROR(condition, "missing key %s in configuration file", key)

int unified_config_file_parse(GlobalConfiguration *global_config,
                             TransportConfiguration *transport_config,
                             ApplicationConfiguration *application_config,
                             StoringConfiguration *storing_config,
                             const char *config_file_path) {
  int status = 0;
  cfg_t *cfg = NULL;
  
  // Variables for parsing
  cfg_bool_t daemonize;
  
#ifdef RS232
  char *flow_control_str = NULL;
  char *parity_str = NULL;
#endif

  // Define configuration sections and their options
  cfg_opt_t global_opts[] = {
    CFG_SIMPLE_STR(CONFIG_LOG_LEVEL, &global_config->log_level),
    CFG_SIMPLE_BOOL(CONFIG_DAEMONIZE, &daemonize),
    CFG_SIMPLE_INT(CONFIG_STOP_AFTER, &global_config->stop_after),
    CFG_SIMPLE_STR(CONFIG_LOG_FILE, &global_config->log_file),
    CFG_END()
  };

#ifdef TCP
  cfg_opt_t tcp_opts[] = {
    CFG_SIMPLE_STR(CONFIG_BIND_IP, &transport_config->bind_ip),
    CFG_SIMPLE_INT(CONFIG_BIND_PORT, &transport_config->bind_port),
    CFG_SIMPLE_INT(CONFIG_RECV_TIMEOUT, &transport_config->recv_timeout),
    CFG_END()
  };
#endif

#ifdef RS232
  cfg_opt_t rs232_opts[] = {
    CFG_SIMPLE_STR(CONFIG_PORT_NAME, &transport_config->port_name),
    CFG_SIMPLE_STR(CONFIG_PARITY, &parity_str),
    CFG_SIMPLE_STR(CONFIG_FLOW_CONTROL, &flow_control_str),
    CFG_SIMPLE_INT(CONFIG_BAUD_RATE, &transport_config->baud_rate),
    CFG_SIMPLE_INT(CONFIG_BITS, &transport_config->bits),
    CFG_SIMPLE_INT(CONFIG_STOP_BITS, &transport_config->stop_bits),
    CFG_END()
  };
#endif

#ifdef HL7
  cfg_opt_t hl7_opts[] = {
    CFG_SIMPLE_INT(CONFIG_DELAY_BEFORE_ACK, &application_config->delay_before_ack),
    CFG_SIMPLE_STR(CONFIG_SENDING_APPLICATION, &application_config->sending_application),
    CFG_SIMPLE_STR(CONFIG_SENDING_FACILITY, &application_config->sending_facility),
    CFG_END()
  };
#endif

#ifdef POCT
  long int response_timeout_ms;
  cfg_opt_t poct_opts[] = {
    CFG_SIMPLE_INT(CONFIG_RESPONSE_TIMEOUT, &response_timeout_ms),
    CFG_END()
  };
#endif

#ifdef FILE_STORING
  cfg_opt_t storing_opts[] = {
    CFG_SIMPLE_STR(CONFIG_STORING_DIRECTORY, &storing_config->storing_directory),
    CFG_END()
  };
#endif

  // Main configuration structure with sections
  cfg_opt_t opts[] = {
    CFG_SEC("global", global_opts, CFGF_NONE),
#ifdef TCP
    CFG_SEC("tcp", tcp_opts, CFGF_NONE),
#endif
#ifdef RS232
    CFG_SEC("rs232", rs232_opts, CFGF_NONE),
#endif
#ifdef HL7
    CFG_SEC("hl7", hl7_opts, CFGF_NONE),
#endif
#ifdef POCT
    CFG_SEC("poct", poct_opts, CFGF_NONE),
#endif
#ifdef FILE_STORING
    CFG_SEC("storing", storing_opts, CFGF_NONE),
#endif
    CFG_END()
  };

  // Initialize and parse configuration
  cfg = cfg_init(opts, 0);
  int return_code = cfg_parse(cfg, config_file_path);

  CHECK_CFG_ERROR(
      return_code != CFG_FILE_ERROR,
      "can't load configuration file %s : confuse can't access the file", 
      config_file_path);

  CHECK_CFG_ERROR(
      return_code != CFG_PARSE_ERROR,
      "can't load configuration file %s : confuse reported a parse error",
      config_file_path);

  // Process global configuration
  global_config->daemonize = daemonize;
  
  // Verify required global keys
  VERIFY_KEY(global_config->log_level != NULL, CONFIG_LOG_LEVEL);

#ifdef TCP
  // Verify required TCP keys
  VERIFY_KEY(transport_config->bind_ip != NULL, CONFIG_BIND_IP);
#endif

#ifdef RS232
  // Process RS232 specific configurations
  if (parity_str) {
    if (strcmp(parity_str, "NONE") == 0) {
      transport_config->parity = SP_PARITY_NONE;
    } else if (strcmp(parity_str, "ODD") == 0) {
      transport_config->parity = SP_PARITY_ODD;
    } else if (strcmp(parity_str, "EVEN") == 0) {
      transport_config->parity = SP_PARITY_EVEN;
    } else {
      CHECK_CFG_ERROR(false, "invalid parity value: %s", parity_str);
    }
  }

  if (flow_control_str) {
    if (strcmp(flow_control_str, "NONE") == 0) {
      transport_config->flow_control = SP_FLOWCONTROL_NONE;
    } else if (strcmp(flow_control_str, "XONXOFF") == 0) {
      transport_config->flow_control = SP_FLOWCONTROL_XONXOFF;
    } else if (strcmp(flow_control_str, "RTSCTS") == 0) {
      transport_config->flow_control = SP_FLOWCONTROL_RTSCTS;
    } else if (strcmp(flow_control_str, "DTRDSR") == 0) {
      transport_config->flow_control = SP_FLOWCONTROL_DTRDSR;
    } else {
      CHECK_CFG_ERROR(false, "invalid flow control value: %s", flow_control_str);
    }
  }
#endif

#ifdef HL7
  // Verify required HL7 keys
  VERIFY_KEY(application_config->sending_application != NULL, CONFIG_SENDING_APPLICATION);
  VERIFY_KEY(application_config->sending_facility != NULL, CONFIG_SENDING_FACILITY);
#endif

#ifdef POCT
  // Process POCT configuration
  application_config->response_timeout_ms = (size_t)response_timeout_ms;
#endif

#ifdef FILE_STORING
  // Verify required storing keys
  VERIFY_KEY(storing_config->storing_directory != NULL, CONFIG_STORING_DIRECTORY);
#endif

clean_exit:
  if (cfg) {
    cfg_free(cfg);
  }

  return status;
}

int unified_config_file_validate(const char *config_file_path) {
  // Basic validation - check if file exists and is readable
  FILE *file = fopen(config_file_path, "r");
  if (!file) {
    log_error("Cannot open configuration file: %s", config_file_path);
    return 1;
  }
  fclose(file);
  
  // Additional validation could be added here to check for required sections
  return 0;
}
