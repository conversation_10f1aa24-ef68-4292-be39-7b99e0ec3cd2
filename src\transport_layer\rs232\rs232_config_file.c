#include "rs232_config_file.h"

#include "log.h"
#include <confuse.h>
#include <libserialport.h>
#include <locale.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/// @brief https://sigrok.org/api/libserialport/unstable/a00004.html
static void display_port_info(struct sp_port *port) {
  /* A pointer to a struct sp_port, which will refer to
   * the port found. */
  /* Display some basic information about the port. */
  printf("Port name: %s\n", sp_get_port_name(port));
  printf("Description: %s\n", sp_get_port_description(port));
  /* Identify the transport which this port is connected through,
   * e.g. native port, USB or Bluetooth. */
  enum sp_transport transport = sp_get_port_transport(port);
  if (transport == SP_TRANSPORT_NATIVE) {
    /* This is a "native" port, usually directly connected
     * to the system rather than some external interface. */
    printf("Type: Native\n");
  } else if (transport == SP_TRANSPORT_USB) {
    /* This is a USB to serial converter of some kind. */
    printf("Type: USB\n");
    /* Display string information from the USB descriptors. */
    printf("Manufacturer: %s\n", sp_get_port_usb_manufacturer(port));
    printf("Product: %s\n", sp_get_port_usb_product(port));
    printf("Serial: %s\n", sp_get_port_usb_serial(port));
    /* Display USB vendor and product IDs. */
    int usb_vid, usb_pid;
    sp_get_port_usb_vid_pid(port, &usb_vid, &usb_pid);
    printf("VID: %04X PID: %04X\n", usb_vid, usb_pid);
    /* Display bus and address. */
    int usb_bus, usb_address;
    sp_get_port_usb_bus_address(port, &usb_bus, &usb_address);
    printf("Bus: %d Address: %d\n", usb_bus, usb_address);
  } else if (transport == SP_TRANSPORT_BLUETOOTH) {
    /* This is a Bluetooth serial port. */
    printf("Type: Bluetooth\n");
    /* Display Bluetooth MAC address. */
    printf("MAC: %s\n", sp_get_port_bluetooth_address(port));
  }
  printf("Freeing port.\n");
  /* Free the port structure created by sp_get_port_by_name(). */
  sp_free_port(port);
  /* Note that this will also free the port name and other
   * strings retrieved from the port structure. If you want
   * to keep these, copy them before freeing the port. */
}

/// @brief https://sigrok.org/api/libserialport/unstable/a00002.html
static void display_ports_info() {
  /* A pointer to a null-terminated array of pointers to
   * struct sp_port, which will contain the ports found.*/
  struct sp_port **port_list;
  log_info("Available ports on the system :");
  /* Call sp_list_ports() to get the ports. The port_list
   * pointer will be updated to refer to the array created. */
  enum sp_return result = sp_list_ports(&port_list);
  if (result != SP_OK) {
    printf("sp_list_ports() failed!\n");
    return;
  }
  /* Iterate through the ports. When port_list[i] is NULL
   * this indicates the end of the list. */
  int i;
  for (i = 0; port_list[i]; i++)
    display_port_info(port_list[i]);
  printf("Found %d ports.\n", i);
  sp_free_port_list(port_list);
}

#define VERIFY_KEY(condition, name)                                            \
  if (!(condition)) {                                                          \
    log_error("Missing key : %s", name);                                       \
    display_ports_info();                                                      \
    status = 1;                                                                \
    goto transport_config_file_parse_clean;                                    \
  }

#define CHECK_CFG_ERROR(condition, ...)                                        \
  if (!(condition)) {                                                          \
    log_error(__VA_ARGS__);                                                    \
    status = 1;                                                                \
    goto transport_config_file_parse_clean;                                    \
  }

static const char *flow_control_to_str[4] = {
    [SP_FLOWCONTROL_NONE] = "NONE",
    [SP_FLOWCONTROL_XONXOFF] = "XONXOFF",
    [SP_FLOWCONTROL_RTSCTS] = "RTSCTS",
    [SP_FLOWCONTROL_DTRDSR] = "DTRDSR",
};

static const char *parity_to_str[5] = {
    [SP_PARITY_NONE] = "NONE",   [SP_PARITY_ODD] = "ODD",
    [SP_PARITY_EVEN] = "EVEN",   [SP_PARITY_MARK] = "MARK",
    [SP_PARITY_SPACE] = "SPACE",
};

int transport_config_file_parse(TransportConfiguration *config,
                                const char *path) {
  int status = 0;
  char *flow_control_str = NULL;
  char *parity_str = NULL;

  // specification
  cfg_opt_t opts[] = {CFG_SIMPLE_STR(CONFIG_PORT_NAME, &config->port_name),
                      CFG_SIMPLE_STR(CONFIG_PARITY, &parity_str),
                      CFG_SIMPLE_STR(CONFIG_FLOW_CONTROL, &flow_control_str),
                      CFG_SIMPLE_INT(CONFIG_BAUD_RATE, &config->baud_rate),
                      CFG_SIMPLE_INT(CONFIG_BITS, &config->bits),
                      CFG_SIMPLE_INT(CONFIG_STOP_BITS, &config->stop_bits),
                      CFG_END()};

  // loading
  cfg_t *cfg = cfg_init(opts, 0);
  int return_code = cfg_parse(cfg, path);

  CHECK_CFG_ERROR(
      return_code != CFG_FILE_ERROR,
      "can't load configuration file %s : confuse can't access the file", path);

  CHECK_CFG_ERROR(
      return_code != CFG_PARSE_ERROR,
      "can't load configuration file %s : confuse reported a parse error",
      path);

  // parity
  for (size_t i = 0; i < 5; i++) {
    if (strcmp(parity_to_str[i], parity_str) == 0) {
      config->parity = i;
      break;
    }
  }

  if (strcmp(parity_str, "INVALID") == 0)
    config->parity = SP_PARITY_INVALID;

  // flow control
  for (size_t i = 0; i < 4; i++) {
    if (strcmp(flow_control_to_str[i], flow_control_str) == 0) {
      config->flow_control = i;
      break;
    }
  }

  // verification
  VERIFY_KEY(config->baud_rate != INI_BAUD_RATE, CONFIG_BAUD_RATE);
  VERIFY_KEY(config->bits != INI_BITS, CONFIG_BITS);
  VERIFY_KEY(config->flow_control != INI_FLOW_CONTROl, CONFIG_FLOW_CONTROL);
  VERIFY_KEY(config->parity != INI_PARITY, CONFIG_PARITY);
  VERIFY_KEY(config->port_name != NULL, CONFIG_PORT_NAME);
  VERIFY_KEY(config->stop_bits != INI_STOP_BITS, CONFIG_STOP_BITS);

transport_config_file_parse_clean:
  cfg_free(cfg);

  return status;
}
