#include "rs232_config.h"

#include "global_config_file.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void transport_configuration_init(TransportConfiguration *config) {
  config->baud_rate = INI_BAUD_RATE;
  config->bits = INI_BITS;
  config->flow_control = INI_FLOW_CONTROl;
  config->parity = INI_PARITY;
  config->port_name = NULL;
  config->stop_bits = INI_STOP_BITS;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void transport_configuration_destruct(TransportConfiguration *config) {
  if (!config)
    return;

  FREE_AND_SET_NULL(config->port_name);
}

char *transport_configuration_to_string(const TransportConfiguration *config) {
  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Configuration:\n"
           "  port name: %s\n"
           "  baud rate: %zu\n"
           "  bits: %zu\n"
           "  flow control: %d\n"
           "  parity: %d\n"
           "  stop bits: %zu\n",
           !config->port_name ? "NULL" : config->port_name, config->baud_rate,
           config->bits, config->flow_control, config->parity,
           config->stop_bits);

  return result;
}